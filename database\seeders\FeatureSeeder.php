<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class FeatureSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Inglés
        \App\Models\Feature::create([
            'titulo' => 'Caracteristica 1',
            'contenido' => 'Descripción breve del beneficio.',
            'imagen' => 'media/images/features/image_1.jpg',
            'publicado' => true,
            'orden' => 1,
            'language_id' => 2,
        ]);

        \App\Models\Feature::create([
            'titulo' => 'Caracteristica 2',
            'contenido' => 'Descripción breve del beneficio.',
            'imagen' => 'media/images/features/image_2.jpg',
            'publicado' => true,
            'orden' => 2,
            'language_id' => 2,
        ]);

        \App\Models\Feature::create([
            'titulo' => 'Caracteristica 3',
            'contenido' => 'Descripción breve del beneficio.',
            'imagen' => 'media/images/features/image_3.jpg',
            'publicado' => true,
            'orden' => 3,
            'language_id' => 2,
        ]);

        // Español

        \App\Models\Feature::create([
            'titulo' => 'Feature 1',
            'contenido' => 'Descripción breve del beneficio.',
            'imagen' => 'media/images/features/image_1.jpg',
            'publicado' => true,
            'orden' => 1,
            'language_id' => 1,
        ]);

        \App\Models\Feature::create([
            'titulo' => 'Feature 2',
            'contenido' => 'Descripción breve del beneficio.',
            'imagen' => 'media/images/features/image_2.jpg',
            'publicado' => true,
            'orden' => 2,
            'language_id' => 1,
        ]);

        \App\Models\Feature::create([
            'titulo' => 'Feature 3',
            'contenido' => 'Descripción breve del beneficio.',
            'imagen' => 'media/images/features/image_3.jpg',
            'publicado' => true,
            'orden' => 3,
            'language_id' => 1,
        ]);

        // Alemán

        \App\Models\Feature::create([
            'titulo' => 'Eigenschaften  1',
            'contenido' => 'Descripción breve del beneficio.',
            'imagen' => 'media/images/features/image_1.jpg',
            'publicado' => true,
            'orden' => 1,
            'language_id' => 3,
        ]);

        \App\Models\Feature::create([
            'titulo' => 'Eigenschaften  2',
            'contenido' => 'Descripción breve del beneficio.',
            'imagen' => 'media/images/features/image_2.jpg',
            'publicado' => true,
            'orden' => 2,
            'language_id' => 3,
        ]);

        \App\Models\Feature::create([
            'titulo' => 'Eigenschaften  3',
            'contenido' => 'Descripción breve del beneficio.',
            'imagen' => 'media/images/features/image_3.jpg',
            'publicado' => true,
            'orden' => 3,
            'language_id' => 3,
        ]);
    }
}
