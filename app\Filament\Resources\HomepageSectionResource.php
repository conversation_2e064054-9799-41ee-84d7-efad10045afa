<?php

namespace App\Filament\Resources;

use App\Filament\Resources\HomepageSectionResource\Pages;
use App\Filament\Resources\HomepageSectionResource\RelationManagers;
use App\Models\HomepageSection;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class HomepageSectionResource extends Resource
{
    protected static ?string $model = HomepageSection::class;

    protected static ?string $navigationIcon = 'heroicon-o-rectangle-stack';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\TextInput::make('nombre')->required(),
                Forms\Components\TextInput::make('slug')->required(),
                Forms\Components\TextInput::make('tipo')->required(),
                Forms\Components\TextInput::make('slug_categoria'),
                Forms\Components\TextInput::make('orden')->required(),
                Forms\Components\Toggle::make('visible')->required(),
                Forms\Components\TextInput::make('limite')->required(),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('nombre')->sortable(),
                Tables\Columns\TextColumn::make('slug')->sortable(),
                Tables\Columns\TextColumn::make('tipo')->sortable(),
                Tables\Columns\TextColumn::make('slug_categoria')->sortable(),
                Tables\Columns\TextColumn::make('orden')->sortable(),
                Tables\Columns\ToggleColumn::make('visible')->sortable(),
                Tables\Columns\TextColumn::make('limite')->sortable(),
            ])
            ->reorderable('orden')
            ->defaultSort('orden')
            ->reorderRecordsTriggerAction(function (Tables\Actions\Action $action, bool $isReordering) {
                return $action->button();
            })
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListHomepageSections::route('/'),
            'create' => Pages\CreateHomepageSection::route('/create'),
            'edit' => Pages\EditHomepageSection::route('/{record}/edit'),
        ];
    }
}
