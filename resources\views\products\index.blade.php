<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="csrf-token" content="{{ csrf_token() }}">

    <title>{{ config('app.name', 'Laravel') }} - Productos</title>

    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.bunny.net">
    <link href="https://fonts.bunny.net/css?family=figtree:400,500,600&display=swap" rel="stylesheet" />

    <!-- Scripts -->
    @vite(['resources/css/app.css', 'resources/js/app.js'])
    @livewireStyles
</head>
<body class="font-sans antialiased">
    <div class="min-h-screen bg-gray-100">
        <!-- Navigation -->
        <nav class="bg-white shadow">
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                <div class="flex justify-between h-16">
                    <div class="flex items-center">
                        <a href="{{ route('home') }}" class="text-xl font-semibold text-gray-900">
                            {{ config('app.name', 'Laravel') }}
                        </a>
                        <div class="ml-10 flex items-baseline space-x-4">
                            <a href="{{ route('products.index') }}" class="text-blue-600 hover:text-blue-800 px-3 py-2 rounded-md text-sm font-medium">
                                Productos
                            </a>
                            @auth
                                <a href="{{ route('orders.index') }}" class="text-gray-700 hover:text-gray-900 px-3 py-2 rounded-md text-sm font-medium">
                                    Mis Pedidos
                                </a>
                            @endauth
                        </div>
                    </div>
                    
                    <div class="flex items-center space-x-4">
                        <!-- Shopping Cart Component -->
                        <livewire:shopping-cart />
                        
                        @auth
                            <a href="{{ route('dashboard') }}" class="text-gray-700 hover:text-gray-900">Dashboard</a>
                            <form method="POST" action="{{ route('logout') }}" class="inline">
                                @csrf
                                <button type="submit" class="text-gray-700 hover:text-gray-900">Logout</button>
                            </form>
                        @else
                            <a href="{{ route('login') }}" class="text-gray-700 hover:text-gray-900">Login</a>
                            <a href="{{ route('register') }}" class="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700">Register</a>
                        @endauth
                    </div>
                </div>
            </div>
        </nav>

        <!-- Page Content -->
        <main class="py-12">
            <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
                <!-- Page Header -->
                <div class="mb-8">
                    <h1 class="text-3xl font-bold text-gray-900">Nuestros Productos</h1>
                    <p class="text-gray-600 mt-2">Descubre nuestra selección de cursos y productos digitales</p>
                </div>

                <!-- Product List Component -->
                <livewire:product-list />
            </div>
        </main>
    </div>

    @livewireScripts
    
    <!-- Alpine.js for interactive components -->
    <script defer src="https://unpkg.com/alpinejs@3.x.x/dist/cdn.min.js"></script>
    
    <!-- Listen for cart updates -->
    <script>
        document.addEventListener('livewire:init', () => {
            Livewire.on('addToCart', (event) => {
                // Find the shopping cart component and trigger addToCart
                Livewire.find(document.querySelector('[wire\\:id]').getAttribute('wire:id')).call('addToCart', event.productId, event.quantity);
            });
        });
    </script>
</body>
</html>
