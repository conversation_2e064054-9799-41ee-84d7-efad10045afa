<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="csrf-token" content="{{ csrf_token() }}">

    <title>{{ config('app.name', 'Laravel') }} - Carrito de Compras</title>

    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.bunny.net">
    <link href="https://fonts.bunny.net/css?family=figtree:400,500,600&display=swap" rel="stylesheet" />

    <!-- Scripts -->
    @vite(['resources/css/app.css', 'resources/js/app.js'])
</head>
<body class="font-sans antialiased">
    <div class="min-h-screen bg-gray-100">
        <!-- Navigation -->
        <nav class="bg-white shadow">
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                <div class="flex justify-between h-16">
                    <div class="flex items-center">
                        <a href="{{ route('home') }}" class="text-xl font-semibold text-gray-900">
                            {{ config('app.name', 'Laravel') }}
                        </a>
                    </div>
                    <div class="flex items-center space-x-4">
                        <span id="cart-count" class="bg-blue-600 text-white px-2 py-1 rounded-full text-sm">0</span>
                        @auth
                            <a href="{{ route('dashboard') }}" class="text-gray-700 hover:text-gray-900">Dashboard</a>
                        @else
                            <a href="{{ route('login') }}" class="text-gray-700 hover:text-gray-900">Login</a>
                        @endauth
                    </div>
                </div>
            </div>
        </nav>

        <!-- Page Content -->
        <main class="py-12">
            <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
                <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                    <div class="p-6 text-gray-900">
                        <h1 class="text-2xl font-bold mb-6">Carrito de Compras</h1>

                        <!-- Cart Items -->
                        <div id="cart-items" class="mb-8">
                            <div id="empty-cart" class="text-center py-8">
                                <p class="text-gray-600 mb-4">Tu carrito está vacío</p>
                                <a href="{{ route('home') }}" class="bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700">
                                    Continuar Comprando
                                </a>
                            </div>
                        </div>

                        <!-- Cart Summary -->
                        <div id="cart-summary" class="hidden border-t pt-6">
                            <div class="flex justify-between items-center mb-4">
                                <span class="text-lg font-semibold">Total:</span>
                                <span id="cart-total" class="text-lg font-bold">€0.00</span>
                            </div>
                            
                            <div class="flex space-x-4">
                                @auth
                                    <a href="{{ route('checkout.index') }}" id="checkout-btn" class="bg-green-600 text-white px-6 py-2 rounded-lg hover:bg-green-700">
                                        Proceder al Checkout
                                    </a>
                                @else
                                    <a href="{{ route('login') }}" class="bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700">
                                        Iniciar Sesión para Comprar
                                    </a>
                                @endauth
                                
                                <button id="clear-cart" class="bg-red-600 text-white px-6 py-2 rounded-lg hover:bg-red-700">
                                    Vaciar Carrito
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <script>
        // Cart functionality
        let cart = {};

        // Load cart on page load
        document.addEventListener('DOMContentLoaded', function() {
            loadCart();
        });

        function loadCart() {
            fetch('/cart/')
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        cart = data.cart;
                        updateCartDisplay();
                        updateCartCount(data.count);
                        updateCartTotal(data.total);
                    }
                })
                .catch(error => console.error('Error loading cart:', error));
        }

        function updateCartDisplay() {
            const cartItems = document.getElementById('cart-items');
            const emptyCart = document.getElementById('empty-cart');
            const cartSummary = document.getElementById('cart-summary');

            if (Object.keys(cart).length === 0) {
                emptyCart.style.display = 'block';
                cartSummary.classList.add('hidden');
            } else {
                emptyCart.style.display = 'none';
                cartSummary.classList.remove('hidden');
                
                // Display cart items
                let itemsHtml = '';
                for (const [productId, item] of Object.entries(cart)) {
                    itemsHtml += `
                        <div class="flex justify-between items-center py-4 border-b" data-product-id="${productId}">
                            <div>
                                <h3 class="font-medium">${item.titulo}</h3>
                                <p class="text-sm text-gray-600">€${item.precio} cada uno</p>
                            </div>
                            <div class="flex items-center space-x-4">
                                <div class="flex items-center space-x-2">
                                    <button onclick="updateQuantity(${productId}, ${item.quantity - 1})" class="bg-gray-200 px-2 py-1 rounded">-</button>
                                    <span class="px-3">${item.quantity}</span>
                                    <button onclick="updateQuantity(${productId}, ${item.quantity + 1})" class="bg-gray-200 px-2 py-1 rounded">+</button>
                                </div>
                                <span class="font-medium">€${(item.precio * item.quantity).toFixed(2)}</span>
                                <button onclick="removeItem(${productId})" class="text-red-600 hover:text-red-800">Eliminar</button>
                            </div>
                        </div>
                    `;
                }
                
                cartItems.innerHTML = itemsHtml;
            }
        }

        function updateQuantity(productId, newQuantity) {
            if (newQuantity <= 0) {
                removeItem(productId);
                return;
            }

            fetch('/cart/update', {
                method: 'PUT',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                },
                body: JSON.stringify({
                    product_id: productId,
                    quantity: newQuantity
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    cart = data.cart;
                    updateCartDisplay();
                    updateCartCount(data.count);
                    updateCartTotal(data.total);
                }
            })
            .catch(error => console.error('Error updating cart:', error));
        }

        function removeItem(productId) {
            fetch('/cart/remove', {
                method: 'DELETE',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                },
                body: JSON.stringify({
                    product_id: productId
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    cart = data.cart;
                    updateCartDisplay();
                    updateCartCount(data.count);
                    updateCartTotal(data.total);
                }
            })
            .catch(error => console.error('Error removing item:', error));
        }

        function updateCartCount(count) {
            document.getElementById('cart-count').textContent = count;
        }

        function updateCartTotal(total) {
            document.getElementById('cart-total').textContent = `€${total.toFixed(2)}`;
        }

        // Clear cart
        document.getElementById('clear-cart')?.addEventListener('click', function() {
            if (confirm('¿Estás seguro de que quieres vaciar el carrito?')) {
                fetch('/cart/clear', {
                    method: 'DELETE',
                    headers: {
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                    }
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        cart = {};
                        updateCartDisplay();
                        updateCartCount(0);
                        updateCartTotal(0);
                    }
                })
                .catch(error => console.error('Error clearing cart:', error));
            }
        });
    </script>
</body>
</html>
