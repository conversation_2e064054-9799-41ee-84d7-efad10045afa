<?php

namespace App\Http\Controllers;

use App\Models\Order;
use App\Models\OrderItem;
use App\Models\Product;
use App\Services\PaymentService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Session;
use Illuminate\View\View;

class CheckoutController extends Controller
{
    protected PaymentService $paymentService;

    public function __construct(PaymentService $paymentService)
    {
        $this->middleware('auth');
        $this->paymentService = $paymentService;
    }

    /**
     * Show checkout page
     */
    public function index(): View|RedirectResponse
    {
        $cart = Session::get('cart', []);

        if (empty($cart)) {
            return redirect()->route('cart.index')
                ->with('error', 'Tu carrito está vacío.');
        }

        // Validate cart items
        $validatedCart = $this->validateCartItems($cart);

        if ($validatedCart['has_changes']) {
            Session::put('cart', $validatedCart['cart']);
            return redirect()->route('checkout.index')
                ->with('warning', 'Algunos productos en tu carrito han cambiado. Por favor revisa tu pedido.');
        }

        $availableGateways = $this->paymentService->getAvailableGateways();

        return view('checkout.index', [
            'cart' => $validatedCart['cart'],
            'total' => $validatedCart['total'],
            'gateways' => $availableGateways,
        ]);
    }

    /**
     * Process checkout and create order
     */
    public function process(Request $request): JsonResponse|RedirectResponse
    {
        $request->validate([
            'gateway' => 'required|string|in:stripe,paypal',
            'terms' => 'accepted',
        ]);

        $cart = Session::get('cart', []);

        if (empty($cart)) {
            return response()->json([
                'success' => false,
                'message' => 'Tu carrito está vacío.',
            ], 400);
        }

        // Validate cart one more time
        $validatedCart = $this->validateCartItems($cart);

        if ($validatedCart['has_changes']) {
            Session::put('cart', $validatedCart['cart']);
            return response()->json([
                'success' => false,
                'message' => 'Algunos productos han cambiado. Por favor revisa tu carrito.',
                'reload_required' => true,
            ], 400);
        }

        try {
            DB::beginTransaction();

            // Create order
            $order = Order::create([
                'user_id' => Auth::id(),
                'total' => $validatedCart['total'],
                'estado' => 'pendiente',
                'moneda' => config('payments.currency', 'EUR'),
            ]);

            // Create order items
            foreach ($validatedCart['cart'] as $item) {
                OrderItem::create([
                    'order_id' => $order->id,
                    'product_id' => $item['id'],
                    'cantidad' => $item['quantity'],
                    'precio_unitario' => $item['precio'],
                    'subtotal' => $item['precio'] * $item['quantity'],
                ]);
            }

            // Create payment record
            $payment = $this->paymentService->createPayment($order, $request->gateway);

            // Process payment with gateway
            $paymentResult = $this->paymentService->processPayment($payment);

            if (!$paymentResult['success']) {
                DB::rollBack();
                return response()->json([
                    'success' => false,
                    'message' => $paymentResult['error'] ?? 'Error procesando el pago.',
                ], 400);
            }

            DB::commit();

            // Clear cart after successful order creation
            Session::forget('cart');

            if ($paymentResult['completed']) {
                // Payment completed immediately (rare)
                return response()->json([
                    'success' => true,
                    'redirect_url' => route('checkout.success', ['order' => $order->id]),
                ]);
            } else {
                // Redirect to payment gateway
                return response()->json([
                    'success' => true,
                    'redirect_url' => $paymentResult['redirect_url'],
                    'redirect_method' => $paymentResult['redirect_method'] ?? 'GET',
                    'redirect_data' => $paymentResult['redirect_data'] ?? [],
                ]);
            }
        } catch (\Exception $e) {
            DB::rollBack();

            Log::error('Checkout process failed', [
                'user_id' => Auth::id(),
                'error' => $e->getMessage(),
                'cart' => $cart,
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Error procesando tu pedido. Por favor intenta de nuevo.',
            ], 500);
        }
    }

    /**
     * Show success page
     */
    public function success(Request $request): View|RedirectResponse
    {
        $orderId = $request->route('order');
        $order = Order::where('id', $orderId)
            ->where('user_id', Auth::id())
            ->with(['items.product', 'payments'])
            ->first();

        if (!$order) {
            return redirect()->route('home')
                ->with('error', 'Pedido no encontrado.');
        }

        return view('checkout.success', compact('order'));
    }

    /**
     * Show cancel page
     */
    public function cancel(Request $request): View
    {
        return view('checkout.cancel');
    }

    /**
     * Validate cart items and update prices/availability
     */
    private function validateCartItems(array $cart): array
    {
        $validCart = [];
        $hasChanges = false;
        $total = 0;

        foreach ($cart as $productId => $item) {
            $product = Product::find($productId);

            if (!$product || !$product->activo) {
                $hasChanges = true;
                continue; // Skip invalid/inactive products
            }

            // Update price if it changed
            if ($product->precio != $item['precio']) {
                $item['precio'] = $product->precio;
                $hasChanges = true;
            }

            // Update product title if it changed
            if ($product->titulo != $item['titulo']) {
                $item['titulo'] = $product->titulo;
                $hasChanges = true;
            }

            $validCart[$productId] = $item;
            $total += $item['precio'] * $item['quantity'];
        }

        return [
            'cart' => $validCart,
            'total' => round($total, 2),
            'has_changes' => $hasChanges,
        ];
    }

    /**
     * Get checkout summary (AJAX endpoint)
     */
    public function summary(): JsonResponse
    {
        $cart = Session::get('cart', []);

        if (empty($cart)) {
            return response()->json([
                'success' => false,
                'message' => 'Tu carrito está vacío.',
            ], 400);
        }

        $validatedCart = $this->validateCartItems($cart);

        return response()->json([
            'success' => true,
            'cart' => $validatedCart['cart'],
            'total' => $validatedCart['total'],
            'has_changes' => $validatedCart['has_changes'],
        ]);
    }
}
