<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class Product extends Model
{
    protected $fillable = [
        'titulo',
        'descripcion',
        'precio',
        'activo',
        'slug',
    ];

    public function files()
    {
        return $this->belongsToMany(File::class);
    }

    public function courses()
    {
        return $this->belongsToMany(Course::class);
    }
}
