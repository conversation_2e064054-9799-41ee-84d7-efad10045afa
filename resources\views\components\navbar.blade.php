<nav class="bg-gray-800 text-white py-4 fixed top-0 w-full z-50 {{ $class }}">
  <div class="container mx-auto flex justify-between items-center">
    <a href="#" class="text-xl font-bold">{{ $brand }}</a>
    <div class="flex items-center gap-4">
      <ul class="flex space-x-4 items-center">
        @foreach ($links as $link)
          <li><a href="{{ $link['href'] }}" class="hover:underline">{{ $link['label'] }}</a></li>
        @endforeach

        @if (Route::has('login'))
          @auth
              <li>
                <a href="{{ url('/dashboard') }}" class="inline-block py-1.5 text-white dark:text-[#EDEDEC] border border-transparent hover:border-[#3E3E3A] rounded-sm leading-normal">
                    Dashboard
                </a>
              </li>
              <li>
                <form method="POST" action="{{ route('logout') }}">
                  @csrf
                  <button type="submit" class="inline-block py-1.5 text-white dark:text-[#EDEDEC] border hover:border-[#3E3E3A] rounded-sm leading-normal">
                    Cerrar sesión
                  </button>
                </form>
              </li>
          @else
              <li>
                <a href="{{ route('login') }}" class="inline-block py-1.5 text-white dark:text-[#EDEDEC] border hover:border-[#3E3E3A] rounded-sm leading-normal">
                    Log in
                </a>
              </li>
              @if (Route::has('register'))
                  <li>
                    <a href="{{ route('register') }}" class="inline-block py-1.5 text-white dark:text-[#EDEDEC] border hover:border-[#3E3E3A] rounded-sm leading-normal">
                        Register
                    </a>
                  </li>
              @endif
          @endauth
        @endif
      </ul>
      <x-language-selector />
    </div>
  </div>
</nav>
