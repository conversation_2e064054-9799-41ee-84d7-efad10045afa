<?php

use Illuminate\Http\Request;
use App\Livewire\Settings\Profile;
use App\Livewire\Settings\Password;
use App\Livewire\Settings\Appearance;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\HomeController;
use Mcamara\LaravelLocalization\Facades\LaravelLocalization;


Route::post('/set-language', function (Request $request) {
    $locale = $request->input('locale');

    if (in_array($locale, array_keys(LaravelLocalization::getSupportedLocales()))) {
        session(['locale' => $locale]);
        app()->setLocale($locale);
    }

    $redirectUrl = LaravelLocalization::getLocalizedURL($locale, $request->input('redirect', '/'));

    return redirect($redirectUrl);
})->name('language.set');

Route::group([
    'prefix' => LaravelLocalization::setLocale(),
    'middleware' => ['localeSessionRedirect', 'localizationRedirect', 'localeViewPath']
], function () {
    Route::get('/', [HomeController::class, 'index'])->name('home');
});



Route::view('dashboard', 'dashboard')
    ->middleware(['auth', 'verified'])
    ->name('dashboard');

Route::middleware(['auth'])->group(function () {
    Route::redirect('settings', 'settings/profile');

    Route::get('settings/profile', Profile::class)->name('settings.profile');
    Route::get('settings/password', Password::class)->name('settings.password');
    Route::get('settings/appearance', Appearance::class)->name('settings.appearance');
});

require __DIR__ . '/auth.php';
